<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>LawVriksh | Homepage</title>
    <!-- Tailwind CSS CDN -->
    <script src="https://cdn.tailwindcss.com"></script>
    <!-- Google Fonts - Using a serif for headings and a sans-serif for body -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Lora:wght@400;500;600;700&family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
    <style>
        /* Custom styles to match the design */
        body {
            background-color: #FDFBF4;
            font-family: 'Inter', sans-serif;
            color: #212121;
            line-height: 1.7;
            letter-spacing: 0.01em;
        }
        h1, h2, h3, h4, h5, h6 {
            font-family: 'Lora', serif;
            color: #5D4037;
            font-weight: 700;
            line-height: 1.3;
            letter-spacing: -0.02em;
        }
        .font-lora {
            font-family: 'Lora', serif;
        }
        .font-inter {
            font-family: 'Inter', sans-serif;
        }
        /* Enhanced typography */
        .article-title {
            font-weight: 700;
            line-height: 1.2;
            letter-spacing: -0.025em;
        }
        .article-excerpt {
            line-height: 1.6;
            font-weight: 400;
        }
        .meta-text {
            font-weight: 500;
            letter-spacing: 0.02em;
        }
        /* Custom border color for search input */
        .search-border {
            border-color: #C0A063;
            border-width: 1px;
        }
        .category-btn {
            border-color: #C0A063;
            background-color: #FDFBF4;
            transition: all 0.2s ease-in-out;
            font-weight: 600;
            letter-spacing: 0.01em;
        }
        .category-btn.active, .category-btn:hover {
            background-color: #C0A063;
            border-color: #C0A063;
            color: #FDFBF4;
        }
        .follow-btn {
            background-color: #5D4037;
            color: #FDFBF4;
            padding: 8px 18px;
            font-size: 14px;
            font-weight: 600;
            letter-spacing: 0.02em;
        }
        .follow-btn:hover {
            background-color: #800000;
        }
        .icon-btn {
            color: #5D4037;
        }
        .icon-btn:hover {
            color: #800000;
        }
        .text-accent {
            color: #C0A063;
            font-weight: 600;
        }
        .border-accent {
            border-color: #C0A063;
        }
        /* Professional card styling */
        .article-card {
            border: 1px solid #C0A063;
            background-color: #FDFBF4;
            box-shadow: 0 2px 8px rgba(93, 64, 55, 0.1);
        }
        .section-title {
            font-weight: 700;
            letter-spacing: -0.01em;
            border-bottom: 2px solid #C0A063;
        }
    </style>
</head>
<body class="antialiased">

    <!-- Main Container -->
    <div class="container mx-auto px-4 sm:px-6 lg:px-8 max-w-screen-xl">

        <!-- Header -->
        <header class="py-6 border-b border-accent">
            <div class="flex justify-between items-center">
                <!-- Logo -->
                <div class="flex items-center space-x-2">
                    <span class="font-lora font-bold text-xl text-[#5D4037]">LawVriksh</span>
                </div>

                <!-- Navigation -->
                <nav class="hidden md:flex items-center space-x-8">
                    <a href="#" class="text-[#212121] hover:text-[#5D4037] transition-colors">Home</a>
                    <a href="#" class="text-[#212121] hover:text-[#5D4037] transition-colors">About us</a>
                    <a href="#" class="text-[#212121] hover:text-[#5D4037] transition-colors">Contact Us</a>
                </nav>

                <!-- Search and Profile -->
                <div class="flex items-center space-x-4">
                    <div class="relative hidden sm:block">
                        <input type="text" placeholder="Search" class="bg-transparent border search-border py-2 pl-10 pr-4 w-48 focus:outline-none focus:ring-1 focus:ring-[#C0A063]">
                        <svg class="w-4 h-4 absolute left-4 top-1/2 -translate-y-1/2 text-[#5D4037]" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                        </svg>
                    </div>
                    
                </div>
            </div>
        </header>

        <!-- Page Content -->
        <div class="flex flex-col lg:flex-row gap-12 xl:gap-16 py-8">

            <!-- Main Content (Left Column) -->
            <main class="w-full lg:w-2/3">

                <!-- Category Filters -->
                <div class="flex items-center space-x-3 border-b-2 border-accent pb-8 mb-10">
                    <button class="category-btn px-5 py-2 text-sm font-medium flex items-center space-x-2">
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M12 2l.7 4.4L18 7l-2.8 3.2L16 16l-4-2.2L8 16l1.2-5.8L6 7l5.3-.6z"/></svg>
                        <span>Personalized</span>
                    </button>
                    <button class="category-btn active px-5 py-2 text-sm font-medium">Category 1</button>
                    <button class="category-btn px-5 py-2 text-sm font-medium">Category 2</button>
                    <button class="category-btn px-4 py-2 text-sm font-medium">
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><line x1="12" y1="5" x2="12" y2="19"></line><line x1="5" y1="12" x2="19" y2="12"></line></svg>
                    </button>
                    <div class="flex-grow"></div>
                    <button class="icon-btn">
                        <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M19 21l-7-5-7 5V5a2 2 0 0 1 2-2h10a2 2 0 0 1 2 2z"></path></svg>
                    </button>
                </div>


                <!-- Featured Articles -->
                <div class="space-y-12">
                    <!-- Article 1 (Updated) -->
                    <article>
                        <div class="relative mb-10">
                            <!-- Banner Image -->
                            <div class="w-full h-64 overflow-hidden">
                                <img src="https://images.unsplash.com/photo-*************-a8f479c573a9?q=80&w=1200&auto=format&fit=crop" alt="Lady Justice with scales" class="w-full h-full object-cover">
                            </div>
                            <!-- Overlaid Author Image -->
                            <div class="absolute -bottom-8 left-6">
                                <img src="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?q=80&w=150&auto=format&fit=crop" alt="Author Robert Fox" class="w-20 h-20 rounded-full object-cover border-4 border-[#F9F7F4] shadow-md">
                            </div>
                        </div>
                        <h2 class="article-title font-lora text-4xl mb-4">Data Privacy Isn't Optional Anymore - Understanding India's Digital Personal Data</h2>
                        <p class="article-excerpt text-[#212121] mb-6 text-lg">Some critics argue that the doctrine gives unelected judges the power to overrule Parliament, undermining democratic will. However, defenders assert that it's not a judicial overreach</p>
                        <div class="flex items-center text-sm meta-text text-gray-600">
                            <span>09:00AM</span>
                            <span class="mx-3">&bull;</span>
                            <span>12min read</span>
                            <span class="mx-3">&bull;</span>
                            <a href="#" class="text-accent font-medium hover:underline">Corporate Law</a>
                            <span class="mx-2">&bull;</span>
                            <span>By Robert Fox</span>
                            <div class="flex-grow"></div>
                            <button class="icon-btn"><svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M19 21l-7-5-7 5V5a2 2 0 0 1 2-2h10a2 2 0 0 1 2 2z"></path></svg></button>
                            <button class="icon-btn ml-2"><svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><circle cx="12" cy="12" r="1"></circle><circle cx="19" cy="12" r="1"></circle><circle cx="5" cy="12" r="1"></circle></svg></button>
                        </div>
                    </article>

                    <!-- Article 2 -->
                    <article>
                        <div class="relative mb-10">
                            <!-- Banner Image -->
                            <div class="w-full h-64 overflow-hidden">
                                <img src="https://images.unsplash.com/photo-1450101499163-c8848c66ca85?q=80&w=1200&auto=format&fit=crop" alt="Employment Law Banner" class="w-full h-full object-cover">
                            </div>
                            <!-- Overlaid Author Image -->
                            <div class="absolute -bottom-8 left-6">
                                <img src="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?q=80&w=150&auto=format&fit=crop" alt="Author James Ham" class="w-20 h-20 rounded-full object-cover border-4 border-[#FDFBF4] shadow-md">
                            </div>
                        </div>
                        <h2 class="article-title font-lora text-4xl mb-4">Employment Law 101 - What Every Employer and Employee Must Know</h2>
                        <p class="article-excerpt text-[#212121] mb-6 text-lg">Whether you're a founder in a tech startup, a factory floor, or a corporate office, there exists a legal relationship between employer and employee governed by Employment.</p>
                        <div class="flex items-center text-sm text-gray-500">
                            <span>4:00PM</span>
                            <span class="mx-2">&bull;</span>
                            <span>12min read</span>
                            <span class="mx-2">&bull;</span>
                            <a href="#" class="text-accent font-medium hover:underline">Corporate Law</a>
                            <span class="mx-2">&bull;</span>
                            <span>By James Ham</span>
                            <div class="flex-grow"></div>
                            <button class="icon-btn"><svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M19 21l-7-5-7 5V5a2 2 0 0 1 2-2h10a2 2 0 0 1 2 2z"></path></svg></button>
                            <button class="icon-btn ml-2"><svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><circle cx="12" cy="12" r="1"></circle><circle cx="19" cy="12" r="1"></circle><circle cx="5" cy="12" r="1"></circle></svg></button>
                        </div>
                    </article>

                    <!-- Article 3 -->
                    <article>
                        <div class="relative mb-10">
                            <!-- Banner Image -->
                            <div class="w-full h-64 overflow-hidden">
                                <img src="https://images.unsplash.com/photo-1516321497487-e288fb19713f?q=80&w=1200&auto=format&fit=crop" alt="Constitution Banner" class="w-full h-full object-cover">
                            </div>
                            <!-- Overlaid Author Image -->
                            <div class="absolute -bottom-8 left-6">
                                <img src="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?q=80&w=150&auto=format&fit=crop" alt="Author Robert Fox" class="w-20 h-20 rounded-full object-cover border-4 border-[#FDFBF4] shadow-md">
                            </div>
                        </div>
                        <h2 class="article-title font-lora text-4xl mb-4">The Basic Structure Doctrine - The Unshakable Pillar of the Indian Constitution</h2>
                        <p class="article-excerpt text-[#212121] mb-6 text-lg">What if Parliament had the power to amend the Constitution in any way it liked—even remove fundamental rights? This question led to one of India's most significant judicial...</p>
                        <div class="flex items-center text-sm text-gray-500">
                            <span>03:00AM</span>
                            <span class="mx-2">&bull;</span>
                            <span>12min read</span>
                            <span class="mx-2">&bull;</span>
                            <a href="#" class="text-accent font-medium hover:underline">Corporate Law</a>
                            <span class="mx-2">&bull;</span>
                            <span>By Robert Fox</span>
                            <div class="flex-grow"></div>
                            <button class="icon-btn"><svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M19 21l-7-5-7 5V5a2 2 0 0 1 2-2h10a2 2 0 0 1 2 2z"></path></svg></button>
                            <button class="icon-btn ml-2"><svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><circle cx="12" cy="12" r="1"></circle><circle cx="19" cy="12" r="1"></circle><circle cx="5" cy="12" r="1"></circle></svg></button>
                        </div>
                    </article>
                </div>

                <!-- Law Sections -->
                <div class="mt-16 space-y-12">
                    <!-- Corporate Law Section -->
                    <div>
                        <h3 class="section-title font-lora text-2xl mb-8 pb-3">Corporate Law</h3>
                        <div class="grid grid-cols-1 sm:grid-cols-2 gap-x-8 gap-y-10">
                            <!-- Law Card 1 -->
                            <div class="article-card space-y-4 p-6">
                                <img src="https://images.unsplash.com/photo-*************-a8f479c573a9?q=80&w=400&auto=format&fit=crop" alt="Lady Justice with scales" class="w-full h-48 object-cover">
                                <h4 class="font-lora font-bold text-lg leading-snug">How to create a mobile banking app</h4>
                                <p class="article-excerpt text-sm">The mobile banking market is heated nowadays, but some entrepreneurs may fear its complexity.</p>
                                <div class="meta-text text-xs text-gray-600"><span>09:00AM &bull; 12min read &bull; Corporate Law</span></div>
                            </div>
                           <!-- Law Card 2 -->
                            <div class="article-card space-y-4 p-6">
                                <img src="https://images.unsplash.com/photo-*************-a8f479c573a9?q=80&w=400&auto=format&fit=crop" alt="Lady Justice with scales" class="w-full h-48 object-cover">
                                <h4 class="font-lora font-bold text-lg leading-snug">How to create a mobile banking app</h4>
                                <p class="article-excerpt text-sm">The mobile banking market is heated nowadays, but some entrepreneurs may fear its complexity.</p>
                                <div class="meta-text text-xs text-gray-600"><span>09:00AM &bull; 12min read &bull; Corporate Law</span></div>
                            </div>
                        </div>
                    </div>

                    <!-- Constitution Law Section -->
                    <div>
                        <h3 class="section-title font-lora text-2xl mb-8 pb-3">Constitution Law</h3>
                        <div class="grid grid-cols-1 sm:grid-cols-2 gap-x-8 gap-y-10">
                           <!-- Law Card 4 -->
                            <div class="article-card space-y-4 p-6">
                                <img src="https://images.unsplash.com/photo-*************-a8f479c573a9?q=80&w=400&auto=format&fit=crop" alt="Lady Justice with scales" class="w-full h-48 object-cover">
                                <h4 class="font-lora font-bold text-lg leading-snug">How to create a mobile banking app</h4>
                                <p class="article-excerpt text-sm">The mobile banking market is heated nowadays, but some entrepreneurs may fear its complexity.</p>
                                <div class="meta-text text-xs text-gray-600"><span>09:00AM &bull; 12min read &bull; Constitution Law</span></div>
                            </div>
                           <!-- Law Card 5 -->
                            <div class="article-card space-y-4 p-6">
                                <img src="https://images.unsplash.com/photo-*************-a8f479c573a9?q=80&w=400&auto=format&fit=crop" alt="Lady Justice with scales" class="w-full h-48 object-cover">
                                <h4 class="font-lora font-bold text-lg leading-snug">How to create a mobile banking app</h4>
                                <p class="article-excerpt text-sm">The mobile banking market is heated nowadays, but some entrepreneurs may fear its complexity.</p>
                                <div class="meta-text text-xs text-gray-600"><span>09:00AM &bull; 12min read &bull; Constitution Law</span></div>
                            </div>
                        </div>
                    </div>
                </div>

            </main>

            <!-- Sticky Sidebar (Right Column) -->
            <aside class="w-full lg:w-1/3 lg:sticky top-8 self-start">
                <div class="space-y-8">
                    <!-- Trending Section -->
                    <div>
                        <h3 class="font-lora text-xl font-bold mb-4">Trending</h3>
                        <div class="space-y-4">
                            <div class="flex items-start space-x-4">
                                <span class="text-3xl font-lora text-gray-300">01</span>
                                <p class="font-semibold leading-tight pt-1">The Basic Structure Doctrine - The Unshakable Pillar of the Indian Constitution</p>
                            </div>
                            <div class="flex items-start space-x-4">
                                <span class="text-3xl font-lora text-gray-300">02</span>
                                <p class="font-semibold leading-tight pt-1">Employment Law 101 – What Every Employer and Employee Must Know</p>
                            </div>
                            <div class="flex items-start space-x-4">
                                <span class="text-3xl font-lora text-gray-300">03</span>
                                <p class="font-semibold leading-tight pt-1">Data Privacy Isn’t Optional Anymore – Understanding India’s Digital Personal Data </p>
                            </div>
                        </div>
                        <a href="#" class="text-sm text-accent font-medium mt-4 inline-block">See full list</a>
                    </div>

                    <!-- Who to follow Section -->
                    <div>
                        <h3 class="font-lora text-xl font-bold mb-4">Who to follow</h3>
                        <div class="space-y-4">
                            <div class="flex items-center space-x-3">
                                <img src="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?q=80&w=150&auto=format&fit=crop" alt="Robert Fox" class="w-10 h-10 rounded-full object-cover">
                                <div class="flex-grow">
                                    <p class="font-semibold text-sm">Robert Fox</p>
                                    <p class="text-xs text-gray-500">123k followers &bull; Corporate Law</p>
                                </div>
                                <button class="follow-btn">Follow</button>
                            </div>
                            <div class="flex items-center space-x-3">
                                <img src="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?q=80&w=150&auto=format&fit=crop" alt="James ham" class="w-10 h-10 rounded-full object-cover">
                                <div class="flex-grow">
                                    <p class="font-semibold text-sm">James ham</p>
                                    <p class="text-xs text-gray-500">45k followers &bull; Corporate Law</p>
                                </div>
                                <button class="follow-btn">Follow</button>
                            </div>
                            <div class="flex items-center space-x-3">
                                <img src="https://images.unsplash.com/photo-1560250097-0b93528c311a?q=80&w=150&auto=format&fit=crop" alt="Hans senz" class="w-10 h-10 rounded-full object-cover">
                                <div class="flex-grow">
                                    <p class="font-semibold text-sm">Hans senz</p>
                                    <p class="text-xs text-gray-500">12k followers &bull; Corporate Law</p>
                                </div>
                                <button class="follow-btn">Follow</button>
                            </div>
                        </div>
                        <a href="#" class="text-sm text-accent font-medium mt-4 inline-block">See more suggestions</a>
                    </div>

                    <!-- Support Section -->
                    <div class="border-t border-gray-200/80 pt-6">
                        <p class="text-sm text-gray-600 mb-2">For Support Contact here</p>
                        <p class="font-semibold mb-3"><EMAIL></p>
                        <div class="flex items-center space-x-3">
                            <a href="#" class="w-8 h-8 rounded-full bg-gray-200 flex items-center justify-center text-gray-600 hover:bg-gray-300">
                                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16"><path d="M8 0C3.58 0 0 3.58 0 8c0 3.54 2.29 6.53 5.47 7.59.4.07.55-.17.55-.38 0-.19-.01-.82-.01-1.49-2.01.37-2.53-.49-2.69-.94-.09-.23-.48-.94-.82-1.13-.28-.15-.68-.52-.01-.53.63-.01 1.08.58 1.23.82.72 1.21 1.87.87 2.33.66.07-.52.28-.87.51-1.07-1.78-.2-3.64-.89-3.64-3.95 0-.87.31-1.59.82-2.15-.08-.2-.36-1.02.08-2.12 0 0 .67-.21 2.2.82.64-.18 1.32-.27 2-.27.68 0 1.36.09 2 .27 1.53-1.04 2.2-.82 2.2-.82.44 1.1.16 1.92.08 2.12.51.56.82 1.27.82 2.15 0 3.07-1.87 3.75-3.65 3.95.29.25.54.73.54 1.48 0 1.07-.01 1.93-.01 2.2 0 .21.15.46.55.38A8.012 8.012 0 0 0 16 8c0-4.42-3.58-8-8-8z"/></svg>
                            </a>
                             <a href="#" class="w-8 h-8 rounded-full bg-gray-200 flex items-center justify-center text-gray-600 hover:bg-gray-300">
                                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16"><path d="M0 1.146C0 .513.526 0 1.175 0h13.65C15.474 0 16 .513 16 1.146v13.708c0 .633-.526 1.146-1.175 1.146H1.175C.526 16 0 15.487 0 14.854V1.146zm4.943 12.248V6.169H2.542v7.225h2.401zm-1.2-8.212c.837 0 1.358-.554 1.358-1.248-.015-.709-.52-1.248-1.342-1.248-.822 0-1.359.54-1.359 1.248 0 .694.521 1.248 1.327 1.248h.016zm4.908 8.212V9.359c0-.216.016-.432.08-.586.173-.431.568-.878 1.232-.878.869 0 1.216.662 1.216 1.634v3.865h2.401V9.25c0-2.22-1.184-3.252-2.764-3.252-1.274 0-1.845.7-2.165 1.193v.025h-.016a5.54 5.54 0 0 1 .016-.025V6.169h-2.4c.03.678 0 7.225 0 7.225h2.4z"/></svg>
                            </a>
                        </div>
                    </div>
                </div>
            </aside>

        </div>
    </div>

</body>
</html>
